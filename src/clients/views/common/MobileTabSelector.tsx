'use client';

import { useCallback, useMemo, useState } from 'react';
import SidebarNavItem from '../rental-listings/SidebarNavItem';
import { usePathname } from 'next/navigation';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';

type Props = {
  propertyId: number;
};

const MobileTabSelector = ({ propertyId }: Props) => {
  const pathname = usePathname();
  const tabName = useMemo(
    () => pathname?.split('/')[2]?.replace('-', ' '),
    [pathname]
  );
  const [open, setOpen] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setOpen(!open);
  }, [open]);

  return (
    <div
      className="collapse collapse-arrow bg-white shadow-dropdown"
      onClick={onToggle}
    >
      <input
        type="radio"
        name="my-accordion-3"
        checked={open}
        onChange={onToggle}
        className="!min-h-[36px]"
      />
      <div className="w-full flex items-center justify-between collapse-title px-4 py-2 !min-h-[36px] after:!hidden">
        <p
          className={classNames('text-sm font-bold capitalize', {
            'opacity-0': open,
          })}
        >
          {tabName}
        </p>
        <ChevronDownIcon
          className={classNames('w-auto h-[14px]', {
            'opacity-0': open,
          })}
        />
      </div>
      {open && (
        <div className="collapse-content flex flex-col z-[99] gap-3 -mt-[28px] relative">
          <ChevronUpIcon className="w-auto h-[14px] absolute top-1 right-4" />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/overview`}
            title="Overview"
            pagePath="overview"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/location`}
            title="Location"
            pagePath="location"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/descriptions`}
            title="Descriptions"
            pagePath="descriptions"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/general`}
            title="General Info"
            pagePath="general"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/amenities`}
            title="Amenities"
            pagePath="amenities"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/bedroomsandbathrooms`}
            title="Rooms"
            pagePath="bedroomsandbathrooms"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/calendar`}
            title="Calendar"
            pagePath="calendar"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/photo`}
            title="Photos"
            pagePath="photo"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/service`}
            title="Service Providers"
            pagePath="service"
          />
          <SidebarNavItem
            className="p-0"
            href={`/property/${propertyId}/payment-information`}
            title="Payment Info"
            pagePath="payment-information"
          />
        </div>
      )}
    </div>
  );
};

export default MobileTabSelector;
