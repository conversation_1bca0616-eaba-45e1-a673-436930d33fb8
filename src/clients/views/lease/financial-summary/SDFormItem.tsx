'use client';

import { updateSecurityDepositQbInfo } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import DatePicker from '@/clients/components/common/DatePicker';
import Input from '@/clients/ui/input';
import { SecurityDepositReturnInfo } from '@/types/lease';
import { parseDateString } from '@/utils/common';
import { format } from 'date-fns';
import throttle from 'lodash/throttle';
import { memo, useCallback, useState } from 'react';
import toast from 'react-hot-toast';

type DateFieldKeys =
  | 'sd_tenant_bill_payment_date'
  | 'sd_homeowner_bill_payment_date';
type RefFieldKeys =
  | 'sd_tenant_bill_payment_ref'
  | 'sd_homeowner_bill_payment_ref';

type Props = {
  qbInfo: SecurityDepositReturnInfo;
  dateName: DateFieldKeys;
  refName: RefFieldKeys;
  leaseId: number;
};

const SDFormItem = ({ qbInfo, dateName, refName, leaseId }: Props) => {
  const [dt, setDt] = useState<Date | undefined>(
    qbInfo[dateName] ? parseDateString(qbInfo[dateName] as string) : undefined
  );
  const [ref, setRef] = useState<string>((qbInfo[refName] as string) ?? '');

  const onChangeDate = useCallback(
    (dt?: Date) => {
      if (dt) {
        setDt(dt);
        updateSecurityDepositQbInfo({
          security_deposit: String(leaseId),
          [dateName]: format(dt, 'yyyy-MM-dd'),
        })
          .then((data) => {
            console.log('data is', data);
            toast.success('Updated date.');
            revalidateTagByName(`lease-details-${leaseId}`);
          })
          .catch((e) => console.log({ e }));
      }
    },
    [dateName, leaseId]
  );

  const onChangeText = throttle(
    useCallback((e: any) => {
      setRef(e.target.value);
    }, []),
    400
  );

  const onBlurTextInput = useCallback(() => {
    if (ref.trim().length === 0) {
      return;
    }
    updateSecurityDepositQbInfo({
      security_deposit: String(leaseId),
      [refName]: ref,
    })
      .then((data) => {
        console.log('data is', data);
        revalidateTagByName(`lease-details-${leaseId}`);
        toast.success('Updated referrence.');
      })
      .catch((e) => console.log({ e }));
  }, [ref, refName, leaseId]);

  return (
    <>
      <td className="py-2 text-center font-normal w-[18%]">
        <DatePicker
          className="!p-2 rounded-md"
          popOverClassName="p-0 w-full"
          selected={dt}
          onSelect={onChangeDate}
        />
      </td>
      <td className="py-2 text-center font-normal w-[26%] pl-2">
        <Input
          placeholder="Ref"
          value={ref}
          onChange={onChangeText}
          onBlur={onBlurTextInput}
          className="w-full !p-2 !text-xs"
        />
      </td>
    </>
  );
};

export default memo(SDFormItem);
