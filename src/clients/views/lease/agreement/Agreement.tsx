'use client';

import LoadingSpinner from '@/clients/ui/loading-spinner';
import { memo, useState } from 'react';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

type PageProps = {
  fileUrl?: string;
  leaseId: number;
};

const Agreement = ({ fileUrl, leaseId }: PageProps) => {
  const [loading, setLoading] = useState<boolean>(true);

  return (
    <div className="w-full h-full relative">
      {loading && (
        <div className="w-full min-h-[500px] flex items-center justify-center absolute">
          <LoadingSpinner className="w-10 h-10 text-blue-500" />
        </div>
      )}

      <iframe
        src={`https://docs.google.com/viewer?url=${encodeURIComponent(
          fileUrl || `${BASE_URL}/get-sign-file?lease=${leaseId}&download=false`
        )}&embedded=true`}
        className="w-full min-h-[500px] border-0"
        title="Document Viewer"
        onLoad={() => {
          setLoading(false);
        }}
      />
    </div>
  );
};
export default memo(Agreement);
