'use client';

import { memo, useCallback, useMemo } from 'react';

import { DateRange, DayContentProps } from 'react-day-picker';

import {
  addYears,
  differenceInCalendarDays,
  endOfMonth,
  format,
  isBefore,
  isSameDay,
  isValid,
  startOfDay,
  subMonths,
} from 'date-fns';
import { Calendar } from '@/clients/ui/calendar';
import Button from '@/clients/ui/button';
import { PropertyAvailability, PropertyRentalRates } from '@/types/calendar';
import { parseDateString } from '@/utils/common';
import {
  formatDateRangePickerRentalRates,
  getBlockedRangesForDate,
  getBlockedStartAndEndDates,
} from '@/utils/date-range-picker';

type Props = {
  date?: DateRange;
  setDate: (_d?: DateRange) => void;
  onClear: () => void;
  onClose: () => void;
  name?: string;
  rentalRates?: PropertyRentalRates[];
  availableCalendar?: PropertyAvailability[];
  enableAllDates?: boolean;
};

const DateRangePickerComponent = ({
  date,
  setDate,
  onClear,
  onClose,
  name,
  rentalRates = [],
  availableCalendar = [],
  enableAllDates,
}: Props) => {
  const onClearDates = useCallback(() => {
    onClear();
  }, [onClear]);

  const isCheckinSelected = useMemo(
    () => date?.from && isValid(date.from),
    [date?.from]
  );

  const isSelectedValid = useMemo(
    () => !!(date?.from && date?.to && isValid(date.from) && isValid(date.to)),
    [date?.from, date?.to]
  );

  // Format rental rates data
  const rentalRatesMap = useMemo(
    () => formatDateRangePickerRentalRates(rentalRates),
    [rentalRates]
  );

  // Get blocked dates
  const { start: blockedStartDates, end: blockedEndDates } = useMemo(
    () =>
      getBlockedStartAndEndDates(
        availableCalendar.filter(
          (_a) =>
            !isBefore(
              startOfDay(parseDateString(_a.from_date)),
              startOfDay(subMonths(new Date(), 1))
            ) &&
            !isBefore(
              startOfDay(parseDateString(_a.to_date)),
              startOfDay(subMonths(new Date(), 1))
            )
        )
      ),
    [availableCalendar]
  );

  const headerText = useMemo(() => {
    if (isCheckinSelected) {
      if (isSelectedValid && date?.from && date?.to) {
        return `${differenceInCalendarDays(date?.to, date?.from)} nights`;
      }
      return 'Select check-out date';
    } else {
      return 'Select check-in date';
    }
  }, [isCheckinSelected, isSelectedValid, date?.from, date?.to]);

  // Calendar disabled dates
  const calendarDisabledDates = useMemo(
    () =>
      enableAllDates
        ? []
        : [
            {
              before: new Date(),
              after: endOfMonth(subMonths(addYears(new Date(), 2), 1)),
            },
            (dt: Date) => {
              const blockedRanges = getBlockedRangesForDate(
                availableCalendar,
                dt
              );
              const rentalRate = (
                rentalRatesMap[format(dt, 'MMM-yyyy')] ?? []
              ).find((_r) => isSameDay(parseDateString(_r.from_date), dt));

              const isDisabled =
                (blockedRanges.length > 0 &&
                  !blockedStartDates.some((_d) =>
                    isSameDay(startOfDay(dt), startOfDay(_d))
                  ) &&
                  !blockedEndDates.some((_d) =>
                    isSameDay(startOfDay(dt), startOfDay(_d))
                  )) ||
                !rentalRate;

              return isDisabled;
            },
          ],
    [
      enableAllDates,
      availableCalendar,
      rentalRatesMap,
      blockedStartDates,
      blockedEndDates,
    ]
  );

  return (
    <>
      <div className="flex items-center justify-between pb-4">
        <div className="w-1/2 pl-4">
          <p className="m-0 text-xl font-medium">{headerText}</p>
          {isSelectedValid && date?.from && date?.to && (
            <p className="text-xs text-[#6D7380] m-0 mt-2">{`${format(
              date?.from,
              'LLL d, yyyy'
            )} - ${format(date?.to, 'LLL d, yyyy')}`}</p>
          )}
        </div>
      </div>
      <Calendar
        initialFocus
        fromMonth={new Date()}
        toMonth={endOfMonth(subMonths(addYears(new Date(), 2), 1))}
        disabled={calendarDisabledDates}
        mode="range"
        defaultMonth={date?.from}
        selected={date}
        onSelect={setDate}
        numberOfMonths={2}
        id="Calendar"
        className="p-0 [&_*_td>button]:font-normal"
      />
      <div className="flex items-center justify-end gap-x-4">
        <Button
          intent="ghost"
          className="border-solid border-carolina-blue-40 text-sm font-normal underline"
          onClick={onClearDates}
        >
          Clear dates
        </Button>
        <Button
          intent="outline"
          className="text-sm font-normal rounded-[32px]"
          onClick={onClose}
        >
          Close
        </Button>
      </div>
    </>
  );
};

export default memo(DateRangePickerComponent);
