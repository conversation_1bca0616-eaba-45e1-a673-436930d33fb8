{"name": "cnc-odin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@headlessui/react": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slider": "^1.3.5", "date-fns": "^4.1.0", "dayjs": "^1.11.12", "formik": "^2.4.6", "lodash": "^4.17.21", "next": "14.2.5", "quill": "^2.0.2", "rambda": "^9.2.1", "react": "^18", "react-datepicker": "^7.3.0", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hot-toast": "^2.4.1", "react-quill": "^2.0.0", "swr": "^2.2.5", "vaul": "^1.1.2", "yup": "^1.5.0"}, "devDependencies": {"@heroicons/react": "^2.1.5", "@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "daisyui": "^4.12.10", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}